import abc
from typing import List, Tuple


class BaseQuery(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def build(self):
        pass


class AndQuery(BaseQuery):
    keyword = 'filter'

    def __init__(self, *queries, query_type='bool'):
        self.queries = queries
        self.query_type = query_type

    def build(self):
        if len(self.queries) == 1:
            return self.queries[0].build()
        return {
            self.query_type: {self.keyword: [query.build() for query in self.queries]}
        }


class AndQueryWithScore(AndQuery):
    keyword = 'must'


class OrQuery(AndQuery):
    keyword = 'should'


class EqualsQuery(BaseQuery):
    keyword = 'match'

    def __init__(self, filed_name: str, value_to_match: str):
        self.filed_name = filed_name
        self.value_to_match = value_to_match

    def build(self):
        return {self.keyword: {self.filed_name: {"query": self.value_to_match}}}


class InQuery(BaseQuery):
    keyword = 'bool'

    def __init__(self, filed_name, value_to_match: List[str]):
        self.filed_name = filed_name
        self.value_to_match = value_to_match

    def build(self):
        should_clauses = []
        for value in self.value_to_match:
            should_clauses.append({"match": {self.filed_name: {"query": value}}})
        return {self.keyword: {"should": should_clauses}}


class WildCardQuery(BaseQuery):
    keyword = 'wildcard'

    def __init__(self, filed_name: str, value_to_match: str):
        self.filed_name = filed_name
        self.value_to_match = value_to_match

    def build(self):
        field = self.filed_name + ".keyword"
        return {self.keyword: {field: {"value": f"*{self.value_to_match}*", "case_insensitive": True}}}


class RangeQuery(BaseQuery):
    keyword = 'range'

    def __init__(
        self,
        filed_name: str,
        criteria1: Tuple[str, str] = None,
        criteria2: Tuple[str, str] = None,
    ):
        assert criteria1 or criteria2, "Invalid Argument"

        self.filed_name = filed_name
        op1, value1 = criteria1 if criteria1 else (None, None)
        op2, value2 = criteria2 if criteria2 else (None, None)
        math_op_to_es_op_map = {
            '>': 'gt',
            '>=': 'gte',
            '<': 'lt',
            '<=': 'lte',
        }
        self.op1 = math_op_to_es_op_map[op1] if op1 else None
        self.op2 = math_op_to_es_op_map[op2] if op2 else None

        self.value1 = value1
        self.value2 = value2

    def build(self):
        range_query = {self.op1: self.value1} if self.op1 else {}
        if self.op2:
            range_query[self.op2] = self.value2
        return {self.keyword: {self.filed_name: range_query}}


class NestedQuery(BaseQuery):
    keyword = "nested"

    def __init__(self, path, child_query: BaseQuery):
        self.path = path
        self.child_query = child_query

    def build(self):
        return {self.keyword: {"path": self.path, "query": self.child_query.build()}}


class NegateQuery(BaseQuery):
    keyword = 'must_not'

    def __init__(self, query: BaseQuery, query_type='bool'):
        self.query_to_negate = query
        self.query_type = query_type

    def build(self):
        return {self.query_type: {self.keyword: self.query_to_negate.build()}}


class SortCriteria:
    def __init__(self, field_name, sort_order):
        self.field_name = field_name
        self.sort_order = sort_order

    def build(self):
        return {self.field_name: self.sort_order}
