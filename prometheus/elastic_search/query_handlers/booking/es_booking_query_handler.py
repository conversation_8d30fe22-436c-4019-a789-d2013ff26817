import json
import logging
from typing import List

from object_registry import register_instance
from prometheus.common.decorators import timed
from prometheus.elastic_search import indexes
from prometheus.elastic_search.query_handlers.booking.builder import (
    BookingsQueryBuilder,
)
from prometheus.elastic_search.query_handlers.booking.es_booking_query import (
    ESBookingSearchQuery,
)
from prometheus.infrastructure.external_clients.elastic_search_client import (
    ElasticSearchServiceClient,
)


@register_instance(
    dependencies=[
        ElasticSearchServiceClient,
    ]
)
class ESBookingQueryHandler:
    def __init__(
        self,
        elastic_search_service_client: ElasticSearchServiceClient,
    ):
        self.elastic_search_service_client = elastic_search_service_client

    @timed
    def search(self, search_object: ESBookingSearchQuery) -> List[str]:
        query = BookingsQueryBuilder.build(search_object)
        booking_index = indexes.get_booking_index_name()
        print(booking_index)
        results = self.elastic_search_service_client.search(booking_index, query)
        print("=" * 80)
        print("ELASTICSEARCH COMPLETE RESPONSE:")
        print("=" * 80)
        print(json.dumps(results, indent=2, default=str))
        print("=" * 80)
        print(f"Query used: {json.dumps(query, indent=2, default=str)}")
        print("=" * 80)

        extracted_ids = self._extract_booking_ids(results)
        print(f"Extracted booking IDs: {extracted_ids}")
        print("=" * 80)

        return extracted_ids
        # return self._extract_booking_ids(results)

    @timed
    def yield_search(
        self, search_object: ESBookingSearchQuery, scroll="5m"
    ) -> List[str]:
        query = BookingsQueryBuilder.build(search_object)
        booking_index = indexes.get_booking_index_name()

        results = self.elastic_search_service_client.search(
            booking_index, query, scroll=scroll
        )
        scroll_id = results.get("_scroll_id")
        booking_ids = self._extract_booking_ids(results)

        if not booking_ids:
            return []

        yield booking_ids

        while booking_ids and scroll_id:
            results = self.elastic_search_service_client.scroll(
                dict(scroll_id=scroll_id, scroll=scroll)
            )
            scroll_id = results.get("_scroll_id")
            booking_ids = self._extract_booking_ids(results)
            if not booking_ids:
                break
            yield booking_ids

    @timed
    def count(self, search_object: ESBookingSearchQuery):
        query = BookingsQueryBuilder.build(search_object, for_count_only=True)
        result = self.elastic_search_service_client.count(
            indexes.get_booking_index_name(), query
        )
        return result["count"]

    @staticmethod
    def _extract_booking_ids(results: dict):
        return [item["_id"] for item in results["hits"]["hits"]]
