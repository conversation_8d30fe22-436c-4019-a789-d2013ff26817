from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from object_registry import register_instance
from prometheus.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from prometheus.infrastructure.external_clients.service_registry import (
    ServiceRegistryClient,
)
from ths_common.exceptions import DownstreamSystemFailure


class ESBatchSizeExceededException(Exception):
    pass


@register_instance()
class ElasticSearchServiceClient(BaseExternalClient):
    page_map = {
        'create_index': dict(
            type=BaseExternalClient.CallTypes.PUT,
            url_regex="/{index_name}",
        ),
        'add_documents_in_bulk': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{index_name}/_bulk",
        ),
        'search': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{index_name}/_search",
        ),
        'search_scroll': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{index_name}/_search?scroll={scroll}",
        ),
        'scroll': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/_search/scroll",
        ),
        'delete_by_query': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{index_name}/_delete_by_query",
        ),
        'count': dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/{index_name}/_count",
        ),
    }

    def get_domain(self):
        return "http://localhost:9200"
        return ServiceRegistryClient.get_aws_open_search_service_endpoint()

    def create_index(self, index_name, index_definition):
        page_name = 'create_index'
        url_params = dict(index_name=index_name)
        response = self.make_call(
            page_name, data=index_definition, url_parameters=url_params
        )
        if not response.is_success():
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def add_documents_in_bulk(self, index_name, bulk_request):
        page_name = 'add_documents_in_bulk'
        url_params = dict(index_name=index_name)
        response = self.make_call(
            page_name, data_string=bulk_request, url_parameters=url_params
        )
        if not response.is_success():
            if response.status_code == 413:
                raise ESBatchSizeExceededException()
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def search(self, index_name, search_query, scroll=None):
        page_name = 'search' if scroll is None else 'search_scroll'
        url_params = dict(index_name=index_name)
        if scroll:
            url_params['scroll'] = scroll
        response = self.make_call(
            page_name, data=search_query, url_parameters=url_params
        )
        if not response.is_success():
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def scroll(self, scroll_query):
        page_name = 'scroll'
        response = self.make_call(
            page_name,
            data=scroll_query,
        )
        if not response.is_success():
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def delete_by_query(self, index_name, delete_query):
        page_name = 'delete_by_query'
        url_params = dict(index_name=index_name)
        response = self.make_call(
            page_name, data=delete_query, url_parameters=url_params
        )
        if not response.is_success():
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    def count(self, index_name, count_query):
        page_name = 'count'
        url_params = dict(index_name=index_name)
        response = self.make_call(
            page_name, data=count_query, url_parameters=url_params
        )
        if not response.is_success():
            raise DownstreamSystemFailure(
                "Elasticsearch API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return response.json_response

    @staticmethod
    def get_headers():
        headers = BaseExternalClient.get_headers()
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        secret = AwsSecretManager.get_open_search_auth_credential(
            tenant_id=current_tenant_id
        )
        auth_secret = secret.get("auth")
        headers["Authorization"] = auth_secret
        return headers
