#!/bin/bash

# Add the same test data to all possible tenant indexes

INDEXES=("crs-treebo-bookings" "crs-default-bookings" "crs-local-bookings")

for INDEX in "${INDEXES[@]}"; do
    echo "📝 Adding data to $INDEX..."
    
    # Booking 1: <PERSON> (mixed case)
    curl -X POST "http://localhost:9200/$INDEX/_doc/BK001" \
      -H "Content-Type: application/json" \
      -d '{
        "booking_id": "BK001",
        "hotel_id": "HOTEL123",
        "group_name": "John Company Ltd",
        "company_legal_name": "John Company Limited",
        "travel_agent_legal_name": "Travel Agent Inc",
        "status": "confirmed",
        "customers": [
          {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "1234567890"
          }
        ],
        "checkin_date": "2024-01-15T14:00:00"
      }' > /dev/null

    # Booking 2: JOHN <PERSON>OE (uppercase)
    curl -X POST "http://localhost:9200/$INDEX/_doc/BK002" \
      -H "Content-Type: application/json" \
      -d '{
        "booking_id": "BK002",
        "hotel_id": "HOTEL123",
        "group_name": "JOHN BUSINESS CORP",
        "company_legal_name": "John Business Corporation",
        "travel_agent_legal_name": "Premium Travel",
        "status": "confirmed",
        "customers": [
          {
            "name": "JOHN DOE",
            "email": "<EMAIL>",
            "phone": "9876543210"
          }
        ],
        "checkin_date": "2024-01-16T14:00:00"
      }' > /dev/null

    # Booking 3: john johnson (lowercase)
    curl -X POST "http://localhost:9200/$INDEX/_doc/BK003" \
      -H "Content-Type: application/json" \
      -d '{
        "booking_id": "BK003",
        "hotel_id": "HOTEL123",
        "group_name": "john hotels",
        "company_legal_name": "John Hotels Private Limited",
        "travel_agent_legal_name": "Direct Booking",
        "status": "pending",
        "customers": [
          {
            "name": "john johnson",
            "email": "<EMAIL>",
            "phone": "5555551234"
          }
        ],
        "checkin_date": "2024-01-17T14:00:00"
      }' > /dev/null

    # Booking 4: Jane Smith (different name for contrast)
    curl -X POST "http://localhost:9200/$INDEX/_doc/BK004" \
      -H "Content-Type: application/json" \
      -d '{
        "booking_id": "BK004",
        "hotel_id": "HOTEL123",
        "group_name": "Jane Corporation",
        "company_legal_name": "Jane Corporation Limited",
        "travel_agent_legal_name": "Elite Travel",
        "status": "confirmed",
        "customers": [
          {
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "phone": "7777777777"
          }
        ],
        "checkin_date": "2024-01-18T14:00:00"
      }' > /dev/null

    echo "✅ Added data to $INDEX"
done

echo ""
echo "📊 Verifying data in all indexes:"
for INDEX in "${INDEXES[@]}"; do
    COUNT=$(curl -s "http://localhost:9200/$INDEX/_count" | jq '.count')
    echo "$INDEX: $COUNT documents"
done
