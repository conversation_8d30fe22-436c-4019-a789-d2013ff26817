from sqlalchemy import or_
from treebo_commons.utils import dateutils

from object_registry import register_instance
from pos.infrastructure.database.base_repository import BaseRepository
from pos.infrastructure.database.repositories.order.db_adaptors.reservation_adaptor import (
    ReservationAdaptor,
)
from pos.infrastructure.database.repositories.order.models import ReservationModel
from ths_common.pos.constants.order_constants import PosReservationStatus


@register_instance()
class ReservationRepository(BaseRepository):
    reservation_adaptor = ReservationAdaptor()

    def load(self, reservation_id, seller_id):
        reservation_model = (
            self.query(ReservationModel)
            .filter(
                ReservationModel.reservation_id == reservation_id,
                ReservationModel.seller_id == seller_id,
            )
            .first()
        )
        reservation = self.reservation_adaptor.to_domain_entity(reservation_model)
        return reservation

    def save(self, reservation):
        if reservation:
            reservation_model = self.reservation_adaptor.to_db_entity(reservation)
            self._save(reservation_model)
            self.flush_session()
        return reservation

    def update(self, reservation):
        if reservation:
            reservation_model = self.reservation_adaptor.to_db_entity(reservation)
            self._update(reservation_model)
            self.flush_session()
        return reservation

    def get_all_reservations_count(self, seller_id):
        return (
            self.query(ReservationModel)
            .filter(ReservationModel.seller_id == seller_id)
            .count()
        )

    def get_next_reservation_for_table(self, table_id):
        current_datetime = dateutils.current_datetime()
        reservation_model = (
            self.query(ReservationModel)
            .filter(
                ReservationModel.table_id == table_id,
                ReservationModel.start_datetime > current_datetime,
            )
            .order_by(ReservationModel.start_datetime.asc())
            .first()
        )
        reservation = self.reservation_adaptor.to_domain_entity(reservation_model)
        return reservation

    def next_reservation_id(self, seller_id):
        return (
            "S-"
            + seller_id
            + "-R"
            + str(self.get_all_reservations_count(seller_id) + 1)
        )

    def get_reservations(
        self, reservation_search_query, seller_id, current_business_date
    ):
        if not reservation_search_query.start_date:
            start_datetime = dateutils.datetime_at_midnight(current_business_date)
        else:
            start_datetime = dateutils.datetime_at_midnight(
                reservation_search_query.start_date
            )

        if not reservation_search_query.end_date:
            end_datetime = dateutils.datetime_at_max_time_of_day(current_business_date)
        else:
            end_datetime = dateutils.datetime_at_max_time_of_day(
                reservation_search_query.end_date
            )

        query = self.query(ReservationModel).filter(
            ReservationModel.seller_id == seller_id,
            ReservationModel.deleted == False,
            ReservationModel.start_datetime > start_datetime,
            ReservationModel.start_datetime < end_datetime,
        )

        if reservation_search_query.statuses:
            query = query.filter(
                or_(
                    (
                        ReservationModel.status.ilike(order_status)
                        for order_status in reservation_search_query.statuses
                    )
                )
            )

        if reservation_search_query.table_id:
            query = query.filter(
                ReservationModel.table_id == reservation_search_query.table_id
            )

        if reservation_search_query.sort_by:
            if reservation_search_query.sort_by == "-start_datetime":
                reservation_models = query.order_by(
                    ReservationModel.start_datetime.desc()
                )
            elif reservation_search_query.sort_by == "start_datetime":
                reservation_models = query.order_by(
                    ReservationModel.start_datetime.asc()
                )
            else:
                reservation_models = query.order_by(
                    ReservationModel.start_datetime.asc()
                )
        else:
            reservation_models = query.order_by(ReservationModel.start_datetime.asc())

        reservations = [
            self.reservation_adaptor.to_domain_entity(reservation_model)
            for reservation_model in reservation_models
        ]
        return reservations
