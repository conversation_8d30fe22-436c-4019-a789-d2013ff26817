from datetime import timedelta

from sqlalchemy import String, and_, nullslast, or_
from sqlalchemy.orm import load_only
from sqlalchemy.sql.expression import cast
from treebo_commons.utils import dateutils

from object_registry import register_instance
from pos.application.dtos.search_order_query import PosOrderSearchQuery
from pos.domain.order.aggregates.order_aggregate import OrderAggregate
from pos.infrastructure.database.base_repository import BaseRepository
from pos.infrastructure.database.dtos.erp_event_details_dto import ERPOrderDetailsDTO
from pos.infrastructure.database.repositories.order.db_adaptors.order_adaptor import (
    OrderAdaptor,
)
from pos.infrastructure.database.repositories.order.db_adaptors.order_item_adaptor import (
    OrderItemAdaptor,
)
from pos.infrastructure.database.repositories.order.db_adaptors.order_remark_adaptor import (
    OrderRemarkAdaptor,
)
from pos.infrastructure.database.repositories.order.db_adaptors.pos_customer_adaptor import (
    PosCustomerAdaptor,
)
from pos.infrastructure.database.repositories.order.db_adaptors.split_bill_adaptor import (
    SplitBillAdaptor,
)
from pos.infrastructure.database.repositories.order.models import (
    OrderRemarkModel,
    PosCustomerModel,
    SplitBillModel,
)
from pos.models import OrderItemModel, OrderModel
from ths_common.pos.constants.order_constants import (
    PosOrderSettlementMethod,
    PosOrderStatus,
)
from ths_common.utils.common_utils import group_list


@register_instance()
class OrderRepository(BaseRepository):
    order_adaptor = OrderAdaptor()
    order_item_adaptor = OrderItemAdaptor()
    pos_customer_adaptor = PosCustomerAdaptor()
    split_bill_adaptor = SplitBillAdaptor()
    order_remark_adaptor = OrderRemarkAdaptor()

    def from_aggregate(self, aggregate: OrderAggregate = None):
        order = aggregate.order
        order_items = aggregate.get_order_items(include_deleted=True)
        customers = aggregate.get_customers(include_deleted=True)
        split_bills = aggregate.get_split_bills(include_deleted=True)
        order_remarks = aggregate.get_order_remarks(include_deleted=True)

        order_model = self.order_adaptor.to_db_entity(order)

        order_item_models = [
            self.order_item_adaptor.to_db_entity(order_item, order_id=order.order_id)
            for order_item in order_items
        ]

        customer_models = [
            self.pos_customer_adaptor.to_db_entity(customer, order_id=order.order_id)
            for customer in customers
        ]
        split_bill_models = [
            self.split_bill_adaptor.to_db_entity(split_bill, order_id=order.order_id)
            for split_bill in split_bills
        ]

        order_remark_model = [
            self.order_remark_adaptor.to_db_entity(order_remark)
            for order_remark in order_remarks
        ]

        return (
            order_model,
            order_item_models,
            customer_models,
            split_bill_models,
            order_remark_model,
        )

    def save(self, aggregate: OrderAggregate):
        aggregate.check_invariance()
        (
            order_model,
            order_item_models,
            customer_models,
            split_bill_models,
            order_remark_models,
        ) = self.from_aggregate(aggregate)

        self._save(order_model)
        self._save_all(order_item_models)
        self._save_all(customer_models)
        self._save_all(split_bill_models)
        self._save_all(order_remark_models)

        self.flush_session()

    def update(self, aggregate: OrderAggregate):
        aggregate.check_invariance()
        aggregate.increment_version()
        (
            order_model,
            order_item_models,
            customer_models,
            split_bill_models,
            order_remark_models,
        ) = self.from_aggregate(aggregate)
        self._update(order_model)
        self._update_all(order_item_models)
        self._update_all(customer_models)
        self._update_all(split_bill_models)
        self._update_all(order_remark_models)
        self.flush_session()

    def to_aggregate(self, **kwargs):
        order_model = kwargs.get('order_model')
        order_item_models = kwargs.get('order_item_models')
        customer_models = kwargs.get('customer_models')
        split_bill_models = kwargs.get('split_bill_models')
        order_remark_models = kwargs.get('order_remark_models')

        order = self.order_adaptor.to_domain_entity(order_model)
        order_items = [
            self.order_item_adaptor.to_domain_entity(
                order_item, base_currency=order.base_currency
            )
            for order_item in order_item_models
        ]
        if customer_models:
            customers = [
                self.pos_customer_adaptor.to_domain_entity(customer_model)
                for customer_model in customer_models
            ]
        else:
            customers = None
        if split_bill_models:
            split_bills = [
                self.split_bill_adaptor.to_domain_entity(
                    split_bill_model, base_currency=order.base_currency
                )
                for split_bill_model in split_bill_models
            ]
        else:
            split_bills = None

        order_remarks = [
            self.order_remark_adaptor.to_domain_entity(order_remark_model)
            for order_remark_model in order_remark_models
        ]

        return OrderAggregate(
            order,
            order_items,
            customers=customers,
            split_bills=split_bills,
            order_remarks=order_remarks,
        )

    def load(self, order_id, seller_id=None):
        order_query = self.query(OrderModel).filter(OrderModel.order_id == order_id)
        if seller_id is not None:
            order_query = order_query.filter(OrderModel.seller_id == seller_id)
        order_model = order_query.one()
        order_item_models = (
            self.query(OrderItemModel).filter(OrderItemModel.order_id == order_id).all()
        )
        customer_models = (
            self.query(PosCustomerModel)
            .filter(PosCustomerModel.order_id == order_id)
            .all()
        )
        split_bill_models = (
            self.query(SplitBillModel).filter(SplitBillModel.order_id == order_id).all()
        )
        order_remark_models = (
            self.query(OrderRemarkModel)
            .filter(OrderRemarkModel.order_id == order_id)
            .all()
        )

        return self.to_aggregate(
            order_model=order_model,
            order_item_models=order_item_models,
            customer_models=customer_models,
            split_bill_models=split_bill_models,
            order_remark_models=order_remark_models,
        )

    def load_for_update(self, order_id, seller_id=None):
        order_query = self.query(OrderModel).filter(OrderModel.order_id == order_id)
        if seller_id is not None:
            order_query = order_query.filter(OrderModel.seller_id == seller_id)
        order_model = order_query.one()
        order_item_models = (
            self.query(OrderItemModel).filter(OrderItemModel.order_id == order_id).all()
        )
        customer_models = (
            self.query(PosCustomerModel)
            .filter(PosCustomerModel.order_id == order_id)
            .all()
        )
        split_bill_models = (
            self.query(SplitBillModel).filter(SplitBillModel.order_id == order_id).all()
        )
        order_remark_models = (
            self.query(OrderRemarkModel)
            .filter(OrderRemarkModel.order_id == order_id)
            .all()
        )

        return self.to_aggregate(
            order_model=order_model,
            order_item_models=order_item_models,
            customer_models=customer_models,
            split_bill_models=split_bill_models,
            order_remark_models=order_remark_models,
        )

    def _get_query(self, model):
        return self.query(model)

    def _get_scheduled_orders(
        self, query, scheduled_datetime_gte, scheduled_datetime_lte, seller_id
    ):
        include_scheduled_query = self._get_query(OrderModel.order_id)
        include_scheduled_query = include_scheduled_query.filter(
            OrderModel.seller_id == seller_id
        )
        include_scheduled_query = include_scheduled_query.filter(
            OrderModel.scheduled_datetime <= scheduled_datetime_lte
        )
        include_scheduled_query = include_scheduled_query.filter(
            OrderModel.scheduled_datetime >= scheduled_datetime_gte
        )
        # load only order_ids for the original query
        query = query.options(load_only(OrderModel.order_id))

        # union of scheduled orders between specified range and rest of the orders
        sub_query = query.union(include_scheduled_query).subquery()

        return self._get_query(OrderModel).filter(OrderModel.order_id.in_(sub_query))

    def search_orders(self, search_query: PosOrderSearchQuery):
        query = self._get_query(OrderModel)
        include_scheduled = search_query.include_scheduled
        if search_query.seller_id:
            query = query.filter(OrderModel.seller_id == search_query.seller_id)
        if search_query.order_type:
            query = query.filter(OrderModel.order_type == search_query.order_type)
        if search_query.order_number:
            query = query.filter(OrderModel.order_number == search_query.order_number)
        if search_query.crs_booking_id:
            query = query.filter(
                OrderModel.crs_booking_id == search_query.crs_booking_id
            )
        if search_query.room_number:
            query = query.filter(OrderModel.room_number == search_query.room_number)
        if search_query.table_id:
            query = query.filter(OrderModel.table_id == search_query.table_id)
        if search_query.total_price_pretax:
            query = query.filter(
                OrderModel.total_price_pretax == search_query.total_price_pretax
            )
        if search_query.order_date_lte:
            query = query.filter(OrderModel.order_date <= search_query.order_date_lte)
        if search_query.order_date_gte:
            query = query.filter(OrderModel.order_date >= search_query.order_date_gte)
        if search_query.order_datetime_gte:
            query = query.filter(
                OrderModel.order_datetime >= search_query.order_datetime_gte
            )
        if search_query.order_datetime_lte:
            query = query.filter(
                OrderModel.order_datetime <= search_query.order_datetime_lte
            )
        if search_query.is_scheduled is not None:
            if search_query.is_scheduled:
                query = query.filter(OrderModel.scheduled_datetime.isnot(None))
            else:
                query = query.filter(OrderModel.scheduled_datetime.is_(None))
        if search_query.scheduled_datetime_lte and not include_scheduled:
            query = query.filter(
                OrderModel.scheduled_datetime <= search_query.scheduled_datetime_lte
            )
        if search_query.scheduled_datetime_gte and not include_scheduled:
            query = query.filter(
                OrderModel.scheduled_datetime >= search_query.scheduled_datetime_gte
            )
        if search_query.search:
            query = query.filter(
                or_(
                    (cast(OrderModel.order_number, String) == search_query.search),
                    (OrderModel.room_number == search_query.search),
                    (OrderModel.table_id == search_query.search),
                )
            )

        if hasattr(search_query, "order_statuses"):
            query = query.filter(
                or_(
                    (
                        OrderModel.status.ilike(order_status)
                        for order_status in search_query.order_statuses
                    )
                )
            )
        if hasattr(search_query, "order_ids"):
            query = query.filter(OrderModel.order_id.in_(search_query.order_ids))
        # Added this to show scheduled_orders between a time range along with all the other filtered orders.
        # Scheduled orders should start appearing on top of the order list an hour before they are scheduled.
        if search_query.include_scheduled:
            query = self._get_scheduled_orders(
                query,
                search_query.scheduled_datetime_gte,
                search_query.scheduled_datetime_lte,
                search_query.seller_id,
            )

        if search_query.sort_by:
            if search_query.sort_by == "-status":
                query = query.order_by(OrderModel.status.desc())
            elif search_query.sort_by == "status":
                query = query.order_by(OrderModel.status.asc())
            elif search_query.sort_by == "-total_price_pretax":
                query = query.order_by(OrderModel.total_price_pretax.desc())
            elif search_query.sort_by == "total_price_pretax":
                query = query.order_by(OrderModel.total_price_pretax.asc())
            elif search_query.sort_by == "-order_datetime":
                query = query.order_by(OrderModel.order_datetime.desc())
            elif search_query.sort_by == "order_datetime":
                query = query.order_by(OrderModel.order_datetime.asc())
            elif search_query.sort_by == "-scheduled_datetime":
                query = query.order_by(nullslast(OrderModel.scheduled_datetime.desc()))
            elif search_query.sort_by == "scheduled_datetime":
                query = query.order_by(OrderModel.scheduled_datetime.asc())

        else:
            query = query.order_by(OrderModel.created_at.desc())

        if include_scheduled:
            query = query.order_by(OrderModel.created_at.desc())

        if search_query.limit is not None and search_query.offset is not None:
            query = query.limit(search_query.limit).offset(search_query.offset)

        order_models = query.all()
        order_ids = [order_model.order_id for order_model in order_models]
        order_item_models = (
            self.query(OrderItemModel)
            .filter(OrderItemModel.order_id.in_(order_ids))
            .all()
        )
        customer_models = (
            self.query(PosCustomerModel)
            .filter(PosCustomerModel.order_id.in_(order_ids))
            .all()
        )
        split_bill_models = (
            self.query(SplitBillModel)
            .filter(SplitBillModel.order_id.in_(order_ids))
            .all()
        )
        order_remark_models = (
            self.query(OrderRemarkModel)
            .filter(OrderRemarkModel.order_id.in_(order_ids))
            .all()
        )

        grouped_order_item_models = group_list(order_item_models, 'order_id')
        grouped_customer_models = group_list(customer_models, 'order_id')
        grouped_split_bill_models = group_list(split_bill_models, 'order_id')
        grouped_order_remark_models = group_list(order_remark_models, 'order_id')

        order_aggregates = []
        for order_model in order_models:
            order_aggregates.append(
                self.to_aggregate(
                    order_model=order_model,
                    order_item_models=grouped_order_item_models[order_model.order_id],
                    customer_models=grouped_customer_models[order_model.order_id],
                    split_bill_models=grouped_split_bill_models[order_model.order_id],
                    order_remark_models=grouped_order_remark_models[
                        order_model.order_id
                    ],
                )
            )
        return order_aggregates

    def count_orders(self, search_query: PosOrderSearchQuery):
        query = self._get_query(OrderModel)
        include_scheduled = search_query.include_scheduled

        if search_query.seller_id:
            query = query.filter(OrderModel.seller_id == search_query.seller_id)
        if search_query.order_type:
            query = query.filter(OrderModel.order_type == search_query.order_type)
        if search_query.order_number:
            query = query.filter(OrderModel.order_number == search_query.order_number)
        if search_query.crs_booking_id:
            query = query.filter(
                OrderModel.crs_booking_id == search_query.crs_booking_id
            )
        if search_query.room_number:
            query = query.filter(OrderModel.room_number == search_query.room_number)
        if search_query.table_id:
            query = query.filter(OrderModel.table_id == search_query.table_id)
        if search_query.total_price_pretax:
            query = query.filter(
                OrderModel.total_price_pretax == search_query.total_price_pretax
            )
        if search_query.order_date_lte:
            query = query.filter(OrderModel.order_date <= search_query.order_date_lte)
        if search_query.order_date_gte:
            query = query.filter(OrderModel.order_date >= search_query.order_date_gte)
        if search_query.is_scheduled is not None:
            if search_query.is_scheduled:
                query = query.filter(OrderModel.scheduled_datetime.isnot(None))
            else:
                query = query.filter(OrderModel.scheduled_datetime.is_(None))
        if search_query.order_datetime_gte:
            query = query.filter(
                OrderModel.order_datetime >= search_query.order_datetime_gte
            )
        if search_query.order_datetime_lte:
            query = query.filter(
                OrderModel.order_datetime <= search_query.order_datetime_lte
            )
        if search_query.scheduled_datetime_lte and not include_scheduled:
            query = query.filter(
                OrderModel.scheduled_datetime <= search_query.scheduled_datetime_lte
            )
        if search_query.scheduled_datetime_gte and not include_scheduled:
            query = query.filter(
                OrderModel.scheduled_datetime >= search_query.scheduled_datetime_gte
            )
        if search_query.search:
            query = query.filter(
                or_(
                    (cast(OrderModel.order_number, String) == search_query.search),
                    (OrderModel.room_number == search_query.search),
                    (OrderModel.table_id == search_query.search),
                )
            )

        if hasattr(search_query, "order_statuses"):
            query = query.filter(
                or_(
                    (
                        OrderModel.status.ilike(order_status)
                        for order_status in search_query.order_statuses
                    )
                )
            )
        if hasattr(search_query, "order_ids"):
            query = query.filter(OrderModel.order_id.in_(search_query.order_ids))

        if search_query.include_scheduled:
            query = self._get_scheduled_orders(
                query,
                search_query.scheduled_datetime_gte,
                search_query.scheduled_datetime_lte,
                search_query.seller_id,
            )

        return query.count()

    def pos_order_report_query(self, start_date, end_date, seller_id, room_number):
        query = self.query(OrderModel).yield_per(1000)
        query = query.filter(OrderModel.seller_id == seller_id)
        query = query.filter(OrderModel.order_date >= start_date)
        query = query.filter(OrderModel.order_date <= end_date)
        if room_number:
            query = query.filter(OrderModel.room_number == room_number)
        order_models = query.all()
        order_ids = [order_model.order_id for order_model in order_models]
        order_item_models = (
            self.query(OrderItemModel)
            .filter(OrderItemModel.order_id.in_(order_ids))
            .all()
        )
        grouped_order_item_models = group_list(order_item_models, 'order_id')

        order_aggregates = []
        for order_model in order_models:
            order_aggregates.append(
                self.to_aggregate(
                    order_model=order_model,
                    order_item_models=grouped_order_item_models[order_model.order_id],
                )
            )
        return order_aggregates

    def pos_order_report_query_for_multiple_sellers(
        self, start_date, end_date, seller_ids, room_number
    ):
        query = self.query(OrderModel).yield_per(1000)
        query = query.filter(OrderModel.seller_id.in_(seller_ids))
        query = query.filter(OrderModel.order_date >= start_date)
        query = query.filter(OrderModel.order_date <= end_date)
        if room_number:
            query = query.filter(OrderModel.room_number == room_number)
        order_models = query.all()
        order_ids = [order_model.order_id for order_model in order_models]
        order_item_models = (
            self.query(OrderItemModel)
            .filter(OrderItemModel.order_id.in_(order_ids))
            .all()
        )
        grouped_order_item_models = group_list(order_item_models, 'order_id')

        order_aggregates = []
        for order_model in order_models:
            order_aggregates.append(
                self.to_aggregate(
                    order_model=order_model,
                    order_item_models=grouped_order_item_models[order_model.order_id],
                )
            )
        return order_aggregates

    def load_order_ids_with_critical_unsettled_orders(
        self, seller_id, current_business_date
    ):
        q = self._critical_unsettled_order_query(seller_id, current_business_date)
        order_models = q.all()
        return [order.order_id for order in order_models]

    def _critical_unsettled_order_query(self, seller_id, current_business_date):
        q = self.query(OrderModel).filter(
            OrderModel.seller_id == seller_id,
            OrderModel.order_date <= current_business_date,
            OrderModel.status.notin_(
                (
                    PosOrderStatus.VOIDED.value,
                    PosOrderStatus.SETTLED.value,
                    PosOrderStatus.CANCELLED.value,
                )
            ),
        )
        return q

    def get_settled_orders_for_seller(self, seller_ids, order_date):
        q = (
            self.query(OrderModel.order_id, OrderModel.bill_id, OrderModel.seller_id)
            .join(SplitBillModel, SplitBillModel.bill_id == OrderModel.bill_id)
            .filter(
                OrderModel.seller_id.in_(seller_ids),
                OrderModel.order_date == order_date,
                OrderModel.status == PosOrderStatus.SETTLED.value,
                SplitBillModel.status == PosOrderStatus.SETTLED.value,
                SplitBillModel.settlement_method
                == PosOrderSettlementMethod.SETTLE_AT_POS.value,
            )
        )
        orders = q.all()
        order_details = [ERPOrderDetailsDTO(order).__dict__ for order in orders]
        return order_details
